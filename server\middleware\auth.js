const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Middleware untuk verifikasi JWT token
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({ 
                error: 'Token akses diperlukan',
                code: 'NO_TOKEN'
            });
        }

        // Verifikasi token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Cek apakah session masih valid di database
        const sessionQuery = `
            SELECT s.*, u.username, u.role, u.posyandu_name, u.is_active
            FROM sessions s
            JOIN users u ON s.user_id = u.id
            WHERE s.token = ? AND s.expires_at > NOW() AND u.is_active = TRUE
        `;
        
        const sessions = await executeQuery(sessionQuery, [token]);
        
        if (sessions.length === 0) {
            return res.status(401).json({ 
                error: 'Session tidak valid atau telah kedaluwarsa',
                code: 'INVALID_SESSION'
            });
        }

        // Attach user info ke request
        req.user = {
            id: decoded.userId,
            username: sessions[0].username,
            role: sessions[0].role,
            posyandu_name: sessions[0].posyandu_name
        };

        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ 
                error: 'Token tidak valid',
                code: 'INVALID_TOKEN'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ 
                error: 'Token telah kedaluwarsa',
                code: 'TOKEN_EXPIRED'
            });
        }

        console.error('Auth middleware error:', error);
        return res.status(500).json({ 
            error: 'Kesalahan server saat verifikasi token',
            code: 'SERVER_ERROR'
        });
    }
};

// Middleware untuk verifikasi role admin
const requireAdmin = (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        return res.status(403).json({ 
            error: 'Akses ditolak. Hanya admin yang diizinkan.',
            code: 'ADMIN_REQUIRED'
        });
    }
};

// Middleware untuk verifikasi role admin (sama dengan requireAdmin)
const requireAuth = (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        return res.status(403).json({
            error: 'Akses ditolak. Hanya admin yang diizinkan.',
            code: 'ADMIN_REQUIRED'
        });
    }
};

module.exports = {
    authenticateToken,
    requireAdmin,
    requireAuth
};
