const express = require('express');
const { executeQuery } = require('../config/database');
const { 
    hashPassword, 
    verifyPassword, 
    generateTokens, 
    saveSession, 
    removeSession,
    removeAllUserSessions,
    validatePin 
} = require('../utils/auth');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Endpoint login dengan username/password
router.post('/login', async (req, res) => {
    const { username, password, rememberMe = false, deviceInfo } = req.body;
    
    try {
        // Validasi input
        if (!username || !password) {
            return res.status(400).json({ 
                error: 'Username dan password diperlukan',
                code: 'MISSING_CREDENTIALS'
            });
        }

        // Cari user di database
        const userQuery = 'SELECT * FROM users WHERE username = ? AND is_active = TRUE';
        const users = await executeQuery(userQuery, [username]);
        
        if (users.length === 0) {
            return res.status(401).json({ 
                error: 'Username atau password salah',
                code: 'INVALID_CREDENTIALS'
            });
        }

        const user = users[0];

        // Verifikasi password
        const isPasswordValid = await verifyPassword(password, user.password);
        if (!isPasswordValid) {
            return res.status(401).json({ 
                error: 'Username atau password salah',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // Generate tokens
        const { accessToken, refreshToken } = generateTokens(user.id, user.username, user.role);

        // Simpan session
        const clientIp = req.ip || req.connection.remoteAddress;
        await saveSession(user.id, accessToken, refreshToken, deviceInfo, clientIp, rememberMe);

        // Update last login
        await executeQuery('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

        // Response sukses
        res.json({
            success: true,
            message: 'Login berhasil',
            data: {
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    posyandu_name: user.posyandu_name
                },
                tokens: {
                    accessToken,
                    refreshToken,
                    expiresIn: rememberMe ? '30d' : '24h'
                }
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat login',
            code: 'SERVER_ERROR'
        });
    }
});

// Endpoint login dengan PIN
router.post('/login-pin', async (req, res) => {
    const { pin, rememberMe = false, deviceInfo } = req.body;
    
    try {
        // Validasi input
        if (!pin || !validatePin(pin)) {
            return res.status(400).json({ 
                error: 'PIN harus 4-6 digit angka',
                code: 'INVALID_PIN'
            });
        }

        // Cari user berdasarkan PIN
        const userQuery = 'SELECT * FROM users WHERE pin = ? AND is_active = TRUE';
        const users = await executeQuery(userQuery, [pin]);
        
        if (users.length === 0) {
            return res.status(401).json({ 
                error: 'PIN tidak valid',
                code: 'INVALID_PIN'
            });
        }

        const user = users[0];

        // Generate tokens
        const { accessToken, refreshToken } = generateTokens(user.id, user.username, user.role);

        // Simpan session
        const clientIp = req.ip || req.connection.remoteAddress;
        await saveSession(user.id, accessToken, refreshToken, deviceInfo, clientIp, rememberMe);

        // Update last login
        await executeQuery('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

        // Response sukses
        res.json({
            success: true,
            message: 'Login dengan PIN berhasil',
            data: {
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    posyandu_name: user.posyandu_name
                },
                tokens: {
                    accessToken,
                    refreshToken,
                    expiresIn: rememberMe ? '30d' : '24h'
                }
            }
        });

    } catch (error) {
        console.error('PIN login error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat login dengan PIN',
            code: 'SERVER_ERROR'
        });
    }
});

// Endpoint logout
router.post('/logout', authenticateToken, async (req, res) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];
        
        if (token) {
            await removeSession(token);
        }

        res.json({
            success: true,
            message: 'Logout berhasil'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat logout',
            code: 'SERVER_ERROR'
        });
    }
});

// Endpoint logout dari semua device
router.post('/logout-all', authenticateToken, async (req, res) => {
    try {
        await removeAllUserSessions(req.user.id);

        res.json({
            success: true,
            message: 'Logout dari semua device berhasil'
        });

    } catch (error) {
        console.error('Logout all error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat logout semua device',
            code: 'SERVER_ERROR'
        });
    }
});

// Endpoint untuk mendapatkan info user yang sedang login
router.get('/me', authenticateToken, async (req, res) => {
    try {
        const userQuery = `
            SELECT id, username, role, posyandu_name, last_login, created_at,
                   (SELECT COUNT(*) FROM sessions WHERE user_id = users.id AND expires_at > NOW()) as active_sessions
            FROM users 
            WHERE id = ? AND is_active = TRUE
        `;
        
        const users = await executeQuery(userQuery, [req.user.id]);
        
        if (users.length === 0) {
            return res.status(404).json({ 
                error: 'User tidak ditemukan',
                code: 'USER_NOT_FOUND'
            });
        }

        res.json({
            success: true,
            data: {
                user: users[0]
            }
        });

    } catch (error) {
        console.error('Get user info error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat mengambil info user',
            code: 'SERVER_ERROR'
        });
    }
});

// Endpoint untuk verifikasi token
router.get('/verify', authenticateToken, (req, res) => {
    res.json({
        success: true,
        message: 'Token valid',
        data: {
            user: req.user
        }
    });
});

module.exports = router;
