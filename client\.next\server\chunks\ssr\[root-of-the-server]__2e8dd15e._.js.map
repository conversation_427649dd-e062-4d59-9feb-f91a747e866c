{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/unauthorized/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function UnauthorizedPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n      <div className=\"text-center max-w-md\">\n        <div className=\"mb-8\">\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-6\">\n            <svg className=\"w-10 h-10 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n          </div>\n          \n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\"><PERSON><PERSON><PERSON></h1>\n          <p className=\"text-gray-600 mb-8\">\n            <PERSON><PERSON>, <PERSON><PERSON> tidak memiliki izin untuk mengakses halaman ini. \n            Halaman ini hanya dapat diakses oleh admin posyandu.\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <Link \n            href=\"/login\" \n            className=\"inline-flex items-center justify-center w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n            </svg>\n            Login sebagai Admin\n          </Link>\n          \n          <Link \n            href=\"/\" \n            className=\"inline-flex items-center justify-center w-full px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors\"\n          >\n            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n            </svg>\n            Kembali ke Beranda\n          </Link>\n        </div>\n\n        <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\n          <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Butuh Bantuan?</h3>\n          <p className=\"text-xs text-blue-700\">\n            Hubungi admin posyandu untuk mendapatkan akses ke halaman ini.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChF,cAAA,6WAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAIzE,6WAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6WAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,2RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6WAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6WAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;sCAIR,6WAAC,2RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6WAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6WAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;8BAKV,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6WAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}