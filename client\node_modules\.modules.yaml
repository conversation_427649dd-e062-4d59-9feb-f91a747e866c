hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.4.3':
    '@next/env': private
  '@next/swc-win32-x64-msvc@15.4.3':
    '@next/swc-win32-x64-msvc': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@zxing/text-encoding@0.9.0':
    '@zxing/text-encoding': private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  asynckit@0.4.0:
    asynckit: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  client-only@0.0.1:
    client-only: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  csstype@3.1.3:
    csstype: private
  delayed-stream@1.0.0:
    delayed-stream: private
  denque@2.1.0:
    denque: private
  detect-libc@2.0.4:
    detect-libc: private
  dunder-proto@1.0.1:
    dunder-proto: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  generate-function@2.3.1:
    generate-function: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  iconv-lite@0.6.3:
    iconv-lite: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-property@1.0.2:
    is-property: private
  jiti@2.5.1:
    jiti: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lodash@4.17.21:
    lodash: private
  long@5.3.2:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  named-placeholders@1.1.3:
    named-placeholders: private
  nanoid@3.3.11:
    nanoid: private
  object-assign@4.1.1:
    object-assign: private
  picocolors@1.1.1:
    picocolors: private
  postcss@8.5.6:
    postcss: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  require-directory@2.1.1:
    require-directory: private
  rxjs@7.8.2:
    rxjs: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver@7.7.2:
    semver: private
  seq-queue@0.0.5:
    seq-queue: private
  sharp@0.34.3:
    sharp: private
  shell-quote@1.8.3:
    shell-quote: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  sqlstring@2.3.3:
    sqlstring: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  supports-color@8.1.1:
    supports-color: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-custom-error@3.3.1:
    ts-custom-error: private
  tslib@2.8.1:
    tslib: private
  undici-types@6.21.0:
    undici-types: private
  vary@1.1.2:
    vary: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
ignoredBuilds:
  - '@tailwindcss/oxide'
  - sharp
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Thu, 24 Jul 2025 16:40:13 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.5'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@next/swc-darwin-arm64@15.4.3'
  - '@next/swc-darwin-x64@15.4.3'
  - '@next/swc-linux-arm64-gnu@15.4.3'
  - '@next/swc-linux-arm64-musl@15.4.3'
  - '@next/swc-linux-x64-gnu@15.4.3'
  - '@next/swc-linux-x64-musl@15.4.3'
  - '@next/swc-win32-arm64-msvc@15.4.3'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Project\lansia\client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
