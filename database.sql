-- Database setup untuk aplikasi pencatatan kesehatan lansia
-- Pastikan MySQL server sudah ber<PERSON>lan dengan user: root, password: root

-- Buat database jika belum ada
CREATE DATABASE IF NOT EXISTS lansia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Gunakan database lansia
USE lansia;

-- Tabel untuk menyimpan profil lansia
CREATE TABLE IF NOT EXISTS profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    usia INT NOT NULL,
    alamat TEXT NOT NULL,
    riwayat_medis TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel untuk menyimpan riwayat pemeriksaan kesehatan
CREATE TABLE IF NOT EXISTS checkups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id INT NOT NULL,
    tekanan_darah VARCHAR(20) NOT NULL,
    gula_darah INT NOT NULL,
    tanggal DATETIME DEFAULT CURRENT_TIMESTAMP,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- Index untuk optimasi query (skip jika sudah ada)
-- CREATE INDEX idx_profile_id ON checkups(profile_id);
-- CREATE INDEX idx_tanggal ON checkups(tanggal);

-- Tabel untuk menyimpan data admin posyandu
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL, -- hashed password dengan bcrypt
    role ENUM('admin') DEFAULT 'admin',
    posyandu_name VARCHAR(100) NOT NULL,
    pin VARCHAR(6), -- PIN 4-6 digit untuk login cepat
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel untuk menyimpan session dan token management
CREATE TABLE IF NOT EXISTS sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(500) NOT NULL,
    refresh_token VARCHAR(500),
    expires_at DATETIME NOT NULL,
    device_info TEXT,
    ip_address VARCHAR(45),
    is_remember_me BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Index untuk optimasi query authentication
CREATE INDEX idx_username ON users(username);
CREATE INDEX idx_token ON sessions(token);
CREATE INDEX idx_user_sessions ON sessions(user_id);
CREATE INDEX idx_expires_at ON sessions(expires_at);

-- Insert data contoh untuk testing
INSERT INTO profiles (nama, usia, alamat, riwayat_medis) VALUES
('Budi Santoso', 65, 'Jl. Merdeka No. 123, Jakarta', 'Hipertensi, Diabetes'),
('Siti Aminah', 70, 'Jl. Sudirman No. 456, Bandung', 'Kolesterol tinggi'),
('Ahmad Rahman', 68, 'Jl. Gatot Subroto No. 789, Surabaya', 'Asam urat');

INSERT INTO checkups (profile_id, tekanan_darah, gula_darah, catatan) VALUES
(1, '140/90', 180, 'Tekanan darah tinggi, perlu kontrol rutin'),
(1, '130/85', 160, 'Membaik setelah minum obat'),
(2, '120/80', 110, 'Normal'),
(3, '135/88', 140, 'Sedikit tinggi, perlu diet');

-- Insert data admin default akan dilakukan melalui script setup
-- Jalankan: npm run setup

-- Tampilkan struktur tabel
DESCRIBE profiles;
DESCRIBE checkups;
DESCRIBE users;
DESCRIBE sessions;

-- Tampilkan data contoh
SELECT * FROM profiles;
SELECT * FROM checkups;
SELECT id, username, role, posyandu_name, pin, is_active, created_at FROM users;

-- Query untuk testing authentication
-- SELECT u.*, COUNT(s.id) as active_sessions
-- FROM users u
-- LEFT JOIN sessions s ON u.id = s.user_id AND s.expires_at > NOW()
-- GROUP BY u.id;
