{"version": 3, "sources": [], "sections": [{"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/utils/auth.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\n// Interface untuk user data\nexport interface User {\n  id: number;\n  username: string;\n  role: 'admin';\n  posyandu_name: string;\n}\n\n// Interface untuk response login\nexport interface LoginResponse {\n  success: boolean;\n  message: string;\n  data: {\n    user: User;\n    tokens: {\n      accessToken: string;\n      refreshToken: string;\n      expiresIn: string;\n    };\n  };\n}\n\n// Konfigurasi axios dengan interceptor\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n});\n\n// Request interceptor untuk menambahkan token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = getAccessToken();\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor untuk handle token expired\napiClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    if (error.response?.status === 401) {\n      // Token expired atau invalid\n      clearAuthData();\n      \n      // Redirect ke login jika bukan di halaman login\n      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {\n        window.location.href = '/login';\n      }\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Fungsi untuk mendapatkan access token\nexport const getAccessToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem('accessToken');\n};\n\n// Fungsi untuk mendapatkan refresh token\nexport const getRefreshToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem('refreshToken');\n};\n\n// Fungsi untuk mendapatkan user data\nexport const getUser = (): User | null => {\n  if (typeof window === 'undefined') return null;\n  const userStr = localStorage.getItem('user');\n  return userStr ? JSON.parse(userStr) : null;\n};\n\n// Fungsi untuk menyimpan auth data\nexport const setAuthData = (tokens: { accessToken: string; refreshToken: string }, user: User) => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.setItem('accessToken', tokens.accessToken);\n  localStorage.setItem('refreshToken', tokens.refreshToken);\n  localStorage.setItem('user', JSON.stringify(user));\n};\n\n// Fungsi untuk menghapus auth data\nexport const clearAuthData = () => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.removeItem('accessToken');\n  localStorage.removeItem('refreshToken');\n  localStorage.removeItem('user');\n};\n\n// Fungsi untuk cek apakah user sudah login\nexport const isAuthenticated = (): boolean => {\n  return !!getAccessToken() && !!getUser();\n};\n\n// Fungsi untuk cek apakah user adalah admin\nexport const isAdmin = (): boolean => {\n  const user = getUser();\n  return user?.role === 'admin';\n};\n\n\n\n// Fungsi login dengan username/password\nexport const loginWithCredentials = async (\n  username: string, \n  password: string, \n  rememberMe: boolean = false\n): Promise<LoginResponse> => {\n  const response = await apiClient.post('/auth/login', {\n    username,\n    password,\n    rememberMe,\n    deviceInfo: navigator.userAgent\n  });\n  \n  if (response.data.success) {\n    setAuthData(response.data.data.tokens, response.data.data.user);\n  }\n  \n  return response.data;\n};\n\n// Fungsi login dengan PIN\nexport const loginWithPin = async (\n  pin: string, \n  rememberMe: boolean = false\n): Promise<LoginResponse> => {\n  const response = await apiClient.post('/auth/login-pin', {\n    pin,\n    rememberMe,\n    deviceInfo: navigator.userAgent\n  });\n  \n  if (response.data.success) {\n    setAuthData(response.data.data.tokens, response.data.data.user);\n  }\n  \n  return response.data;\n};\n\n// Fungsi logout\nexport const logout = async (): Promise<void> => {\n  try {\n    await apiClient.post('/auth/logout');\n  } catch (error) {\n    console.error('Logout error:', error);\n  } finally {\n    clearAuthData();\n  }\n};\n\n// Fungsi logout dari semua device\nexport const logoutAll = async (): Promise<void> => {\n  try {\n    await apiClient.post('/auth/logout-all');\n  } catch (error) {\n    console.error('Logout all error:', error);\n  } finally {\n    clearAuthData();\n  }\n};\n\n// Fungsi untuk mendapatkan info user yang sedang login\nexport const getCurrentUser = async (): Promise<User> => {\n  const response = await apiClient.get('/auth/me');\n  return response.data.data.user;\n};\n\n// Fungsi untuk verifikasi token\nexport const verifyToken = async (): Promise<boolean> => {\n  try {\n    await apiClient.get('/auth/verify');\n    return true;\n  } catch (error) {\n    return false;\n  }\n};\n\n// Export axios client untuk digunakan di komponen lain\nexport { apiClient };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAEA,MAAM,eAAe;AAwBrB,uCAAuC;AACvC,MAAM,YAAY,wLAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;AACX;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kDAAkD;AAClD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,OAAO;IACL,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,6BAA6B;QAC7B;QAEA,gDAAgD;QAChD,IAAI,gBAAkB,eAAe,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;;IAG1E;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,kBAAkB;IAC7B,wCAAmC,OAAO;;;AAE5C;AAGO,MAAM,UAAU;IACrB,wCAAmC,OAAO;;;IAC1C,MAAM;AAER;AAGO,MAAM,cAAc,CAAC,QAAuD;IACjF,wCAAmC;;;AAKrC;AAGO,MAAM,gBAAgB;IAC3B,wCAAmC;;;AAKrC;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC;AACjC;AAGO,MAAM,UAAU;IACrB,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;AAKO,MAAM,uBAAuB,OAClC,UACA,UACA,aAAsB,KAAK;IAE3B,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,eAAe;QACnD;QACA;QACA;QACA,YAAY,UAAU,SAAS;IACjC;IAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;QACzB,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;IAChE;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,eAAe,OAC1B,KACA,aAAsB,KAAK;IAE3B,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB;QACvD;QACA;QACA,YAAY,UAAU,SAAS;IACjC;IAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;QACzB,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;IAChE;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,SAAS;IACpB,IAAI;QACF,MAAM,UAAU,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;IACjC,SAAU;QACR;IACF;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,UAAU,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;IACrC,SAAU;QACR;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC;IACrC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;AAChC;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,UAAU,GAAG,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/profiles/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { isAuthenticated, isAdmin } from '@/utils/auth';\r\n\r\nexport default function ProfilesRedirectPage() {\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Cek apakah user sudah login dan admin\r\n    if (isAuthenticated() && isAdmin()) {\r\n      // Redirect ke halaman admin profiles\r\n      router.replace('/admin/profiles');\r\n    } else {\r\n      // Redirect ke login\r\n      router.replace('/login');\r\n    }\r\n  }, [router]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\r\n      <div className=\"text-center max-w-md\">\r\n        <div className=\"mb-8\">\r\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-6\">\r\n            <svg className=\"w-10 h-10 text-blue-600 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n            </svg>\r\n          </div>\r\n\r\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Mengalihkan...</h1>\r\n          <p className=\"text-gray-600 mb-8\">\r\n            Halaman daftar lansia telah dipindahkan ke area admin.\r\n            Anda akan dialihkan secara otomatis.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <Link\r\n            href=\"/login\"\r\n            className=\"inline-flex items-center justify-center w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\r\n          >\r\n            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\r\n            </svg>\r\n            Login sebagai Admin\r\n          </Link>\r\n\r\n          <Link\r\n            href=\"/\"\r\n            className=\"inline-flex items-center justify-center w-full px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors\"\r\n          >\r\n            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\r\n            </svg>\r\n            Kembali ke Beranda\r\n          </Link>\r\n        </div>\r\n\r\n        <div className=\"mt-8 p-4 bg-blue-50 rounded-lg\">\r\n          <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Informasi</h3>\r\n          <p className=\"text-xs text-blue-700\">\r\n            Untuk melihat daftar semua lansia, Anda perlu login sebagai admin posyandu terlebih dahulu.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,IAAI,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,OAAO,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD,KAAK;YAClC,qCAAqC;YACrC,OAAO,OAAO,CAAC;QACjB,OAAO;YACL,oBAAoB;YACpB,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;gCAAuC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC9F,cAAA,6WAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAIzE,6WAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6WAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,2RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6WAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6WAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;sCAIR,6WAAC,2RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6WAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6WAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;8BAKV,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6WAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}]}