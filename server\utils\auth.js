const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Hash password dengan bcrypt
const hashPassword = async (password) => {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 10;
    return await bcrypt.hash(password, saltRounds);
};

// Verifikasi password
const verifyPassword = async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
};

// Generate JWT token
const generateTokens = (userId, username, role) => {
    const payload = {
        userId,
        username,
        role
    };

    const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });

    const refreshToken = jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
    });

    return { accessToken, refreshToken };
};

// Simpan session ke database
const saveSession = async (userId, token, refreshToken, deviceInfo, ipAddress, isRememberMe = false) => {
    try {
        // Hapus session lama yang sudah expired
        await executeQuery('DELETE FROM sessions WHERE expires_at < NOW()');
        
        // Tentukan waktu expired
        const expiresHours = isRememberMe ? 
            (parseInt(process.env.REMEMBER_ME_EXPIRES_DAYS) || 30) * 24 : 
            parseInt(process.env.SESSION_EXPIRES_HOURS) || 24;
        
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + expiresHours);

        // Simpan session baru
        const sessionQuery = `
            INSERT INTO sessions (user_id, token, refresh_token, expires_at, device_info, ip_address, is_remember_me)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `;
        
        const result = await executeQuery(sessionQuery, [
            userId, 
            token, 
            refreshToken, 
            expiresAt, 
            deviceInfo || '', 
            ipAddress || '', 
            isRememberMe
        ]);

        return result.insertId;
    } catch (error) {
        console.error('Error saving session:', error);
        throw error;
    }
};

// Hapus session (logout)
const removeSession = async (token) => {
    try {
        await executeQuery('DELETE FROM sessions WHERE token = ?', [token]);
        return true;
    } catch (error) {
        console.error('Error removing session:', error);
        return false;
    }
};

// Hapus semua session user (logout all devices)
const removeAllUserSessions = async (userId) => {
    try {
        await executeQuery('DELETE FROM sessions WHERE user_id = ?', [userId]);
        return true;
    } catch (error) {
        console.error('Error removing all user sessions:', error);
        return false;
    }
};

// Cleanup expired sessions
const cleanupExpiredSessions = async () => {
    try {
        const result = await executeQuery('DELETE FROM sessions WHERE expires_at < NOW()');
        console.log(`🧹 Cleaned up ${result.affectedRows} expired sessions`);
        return result.affectedRows;
    } catch (error) {
        console.error('Error cleaning up expired sessions:', error);
        return 0;
    }
};

// Validasi PIN (4-6 digit)
const validatePin = (pin) => {
    return /^\d{4,6}$/.test(pin);
};

// Generate random PIN
const generateRandomPin = () => {
    return Math.floor(1000 + Math.random() * 9000).toString();
};

module.exports = {
    hashPassword,
    verifyPassword,
    generateTokens,
    saveSession,
    removeSession,
    removeAllUserSessions,
    cleanupExpiredSessions,
    validatePin,
    generateRandomPin
};
