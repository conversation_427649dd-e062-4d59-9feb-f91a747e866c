{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const [loginMode, setLoginMode] = useState<'username' | 'pin'>('username');\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    pin: '',\n    rememberMe: false\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    setError(''); // Clear error saat user mengetik\n  };\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const endpoint = loginMode === 'pin' ? '/auth/login-pin' : '/auth/login';\n      const payload = loginMode === 'pin' \n        ? { \n            pin: formData.pin, \n            rememberMe: formData.rememberMe,\n            deviceInfo: navigator.userAgent \n          }\n        : { \n            username: formData.username, \n            password: formData.password, \n            rememberMe: formData.rememberMe,\n            deviceInfo: navigator.userAgent \n          };\n\n      const response = await axios.post(`${API_BASE_URL}${endpoint}`, payload);\n\n      if (response.data.success) {\n        // Simpan token ke localStorage\n        localStorage.setItem('accessToken', response.data.data.tokens.accessToken);\n        localStorage.setItem('refreshToken', response.data.data.tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(response.data.data.user));\n\n        // Redirect ke dashboard admin\n        router.push('/admin');\n      }\n    } catch (error: any) {\n      console.error('Login error:', error);\n      \n      if (error.response?.data?.error) {\n        setError(error.response.data.error);\n      } else if (error.code === 'ECONNREFUSED') {\n        setError('Tidak dapat terhubung ke server. Pastikan server backend berjalan.');\n      } else {\n        setError('Terjadi kesalahan saat login. Silakan coba lagi.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n        }} />\n      </div>\n\n      <div className=\"relative w-full max-w-md\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <Link href=\"/\" className=\"inline-flex items-center space-x-3 mb-6\">\n            <div className=\"w-12 h-12 bg-blue-600 rounded-2xl flex items-center justify-center shadow-lg\">\n              <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </div>\n            <div className=\"text-left\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Kesehatan Lansia</h1>\n              <p className=\"text-sm text-gray-500\">Admin Posyandu</p>\n            </div>\n          </Link>\n          \n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Masuk ke Dashboard</h2>\n          <p className=\"text-gray-600\">Kelola data kesehatan lansia dengan mudah</p>\n        </div>\n\n        {/* Login Card */}\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n          {/* Mode Toggle */}\n          <div className=\"flex bg-gray-50 border-b border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={() => setLoginMode('username')}\n              className={`flex-1 py-4 px-6 text-sm font-medium transition-colors ${\n                loginMode === 'username'\n                  ? 'bg-white text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <div className=\"flex items-center justify-center space-x-2\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n                <span>Username</span>\n              </div>\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setLoginMode('pin')}\n              className={`flex-1 py-4 px-6 text-sm font-medium transition-colors ${\n                loginMode === 'pin'\n                  ? 'bg-white text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <div className=\"flex items-center justify-center space-x-2\">\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n                <span>PIN</span>\n              </div>\n            </button>\n          </div>\n\n          {/* Login Form */}\n          <form onSubmit={handleLogin} className=\"p-8\">\n            {error && (\n              <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <svg className=\"w-5 h-5 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <p className=\"text-red-700 text-sm\">{error}</p>\n                </div>\n              </div>\n            )}\n\n            {loginMode === 'username' ? (\n              <>\n                <div className=\"mb-4\">\n                  <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Username\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                    placeholder=\"Masukkan username\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n                <div className=\"mb-6\">\n                  <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Password\n                  </label>\n                  <input\n                    type=\"password\"\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                    placeholder=\"Masukkan password\"\n                    required\n                    disabled={isLoading}\n                  />\n                </div>\n              </>\n            ) : (\n              <div className=\"mb-6\">\n                <label htmlFor=\"pin\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  PIN (4-6 digit)\n                </label>\n                <input\n                  type=\"password\"\n                  id=\"pin\"\n                  name=\"pin\"\n                  value={formData.pin}\n                  onChange={handleInputChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-center text-2xl tracking-widest\"\n                  placeholder=\"••••\"\n                  maxLength={6}\n                  pattern=\"[0-9]{4,6}\"\n                  required\n                  disabled={isLoading}\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Masukkan PIN 4-6 digit untuk login cepat</p>\n              </div>\n            )}\n\n            <div className=\"mb-6\">\n              <label className=\"flex items-center space-x-3\">\n                <input\n                  type=\"checkbox\"\n                  name=\"rememberMe\"\n                  checked={formData.rememberMe}\n                  onChange={handleInputChange}\n                  className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                  disabled={isLoading}\n                />\n                <span className=\"text-sm text-gray-700\">Ingat saya selama 30 hari</span>\n              </label>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"animate-spin w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  <span>Memproses...</span>\n                </div>\n              ) : (\n                'Masuk'\n              )}\n            </button>\n          </form>\n\n          {/* Footer */}\n          <div className=\"px-8 py-4 bg-gray-50 border-t border-gray-200\">\n            <div className=\"text-center\">\n              <p className=\"text-xs text-gray-500 mb-2\">Kredensial Default:</p>\n              <div className=\"text-xs text-gray-600\">\n                <div>👨‍⚕️ Admin: <code className=\"bg-gray-200 px-1 rounded\">admin</code> / <code className=\"bg-gray-200 px-1 rounded\">admin123</code> / PIN: <code className=\"bg-gray-200 px-1 rounded\">1234</code></div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Back to Home */}\n        <div className=\"text-center mt-6\">\n          <Link \n            href=\"/\" \n            className=\"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n            </svg>\n            <span>Kembali ke Beranda</span>\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,eAAe;AAEN,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;QACV,KAAK;QACL,YAAY;IACd;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;QACD,SAAS,KAAK,iCAAiC;IACjD;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,cAAc,QAAQ,oBAAoB;YAC3D,MAAM,UAAU,cAAc,QAC1B;gBACE,KAAK,SAAS,GAAG;gBACjB,YAAY,SAAS,UAAU;gBAC/B,YAAY,UAAU,SAAS;YACjC,IACA;gBACE,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,YAAY,UAAU,SAAS;YACjC;YAEJ,MAAM,WAAW,MAAM,2LAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,GAAiB,OAAf,cAAwB,OAAT,WAAY;YAEhE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,+BAA+B;gBAC/B,aAAa,OAAO,CAAC,eAAe,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzE,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY;gBAC3E,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBAEnE,8BAA8B;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAY;gBAGf,sBAAA;YAFJ,QAAQ,KAAK,CAAC,gBAAgB;YAE9B,KAAI,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,KAAK,EAAE;gBAC/B,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;YACpC,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAkB;oBACpB;;;;;;;;;;;0BAGF,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8RAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,4TAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,4TAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAIzC,4TAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCACC,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,0DAIX,OAHC,cAAc,aACV,sDACA;kDAGN,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,4TAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,4TAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,4TAAC;wCACC,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,0DAIX,OAHC,cAAc,QACV,sDACA;kDAGN,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,4TAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,4TAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,4TAAC;gCAAK,UAAU;gCAAa,WAAU;;oCACpC,uBACC,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,4TAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,4TAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;oCAK1C,cAAc,2BACb;;0DACE,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAA+C;;;;;;kEAGnF,4TAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;wDACR,UAAU;;;;;;;;;;;;0DAGd,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAA+C;;;;;;kEAGnF,4TAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;wDACR,UAAU;;;;;;;;;;;;;qEAKhB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAM,SAAQ;gDAAM,WAAU;0DAA+C;;;;;;0DAG9E,4TAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,GAAG;gDACnB,UAAU;gDACV,WAAU;gDACV,aAAY;gDACZ,WAAW;gDACX,SAAQ;gDACR,QAAQ;gDACR,UAAU;;;;;;0DAEZ,4TAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI9C,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CAAM,WAAU;;8DACf,4TAAC;oDACC,MAAK;oDACL,MAAK;oDACL,SAAS,SAAS,UAAU;oDAC5B,UAAU;oDACV,WAAU;oDACV,UAAU;;;;;;8DAEZ,4TAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAI5C,4TAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,0BACC,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,SAAQ;;sEACxD,4TAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,4TAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;8DAErD,4TAAC;8DAAK;;;;;;;;;;;mDAGR;;;;;;;;;;;;0CAMN,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;;oDAAI;kEAAa,4TAAC;wDAAK,WAAU;kEAA2B;;;;;;oDAAY;kEAAG,4TAAC;wDAAK,WAAU;kEAA2B;;;;;;oDAAe;kEAAQ,4TAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjM,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,8RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,4TAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,4TAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,4TAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAtQwB;;QACP,oQAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}