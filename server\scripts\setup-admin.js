require('dotenv').config();
const { executeQuery, testConnection } = require('../config/database');
const { hashPassword } = require('../utils/auth');

async function setupAdmin() {
    try {
        console.log('🔧 Setting up admin user...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            console.error('❌ Database connection failed');
            process.exit(1);
        }

        // Hash password admin
        const adminPassword = await hashPassword('admin123');

        // Check if admin already exists
        const existingAdmin = await executeQuery('SELECT id FROM users WHERE username = ?', ['admin']);
        
        if (existingAdmin.length > 0) {
            console.log('⚠️  Admin user already exists. Updating password...');
            
            // Update existing admin
            await executeQuery(
                'UPDATE users SET password = ?, pin = ? WHERE username = ?',
                [adminPassword, '1234', 'admin']
            );
            
            console.log('✅ Admin password updated successfully');
        } else {
            console.log('👤 Creating new admin user...');
            
            // Create new admin
            await executeQuery(`
                INSERT INTO users (username, password, role, posyandu_name, pin) 
                VALUES (?, ?, ?, ?, ?)
            `, ['admin', adminPassword, 'admin', 'Posyandu Melati', '1234']);
            
            console.log('✅ Admin user created successfully');
        }



        console.log('\n🎉 Setup completed successfully!');
        console.log('\n📋 Default Login Credentials:');
        console.log('   👨‍⚕️ Admin:');
        console.log('     Username: admin');
        console.log('     Password: admin123');
        console.log('     PIN: 1234');
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    }
}

// Run setup
setupAdmin();
