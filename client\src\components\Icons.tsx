import React from 'react';
import { 
  FaHandPaper,
  FaLightbulb,
  FaRocket,
  FaMobile,
  FaQrcode,
  FaFolder,
  FaWrench,
  FaTools,
  FaCog,
  FaPalette,
  FaLock,
  FaShieldAlt,
  FaUsers,
  FaUserPlus,
  FaPhone,
  FaHospital,
  FaHeart,
  FaStethoscope
} from 'react-icons/fa';
import { 
  HiHome,
  HiChartBar,
  HiCalendar,
  HiInformationCircle,
  HiCheckCircle,
  HiExclamationCircle,
  HiXCircle,
  HiSparkles
} from 'react-icons/hi';

interface IconProps {
  className?: string;
  size?: number;
}

const Icons = {
  // Gesture & Emotion Icons
  WavingHand: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHandPaper className={className} size={size} />
  ),

  // Technology & Innovation Icons
  Lightbulb: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaLightbulb className={className} size={size} />
  ),
  Rocket: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaRocket className={className} size={size} />
  ),
  Mobile: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaMobile className={className} size={size} />
  ),
  QRCode: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaQrcode className={className} size={size} />
  ),

  // File & Folder Icons
  FileFolder: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaFolder className={className} size={size} />
  ),

  // Tools & Settings Icons
  Wrench: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaWrench className={className} size={size} />
  ),
  Tools: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaTools className={className} size={size} />
  ),
  Settings: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaCog className={className} size={size} />
  ),
  Palette: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaPalette className={className} size={size} />
  ),

  // Security Icons
  Lock: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaLock className={className} size={size} />
  ),
  Shield: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaShieldAlt className={className} size={size} />
  ),

  // People & Communication Icons
  Users: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaUsers className={className} size={size} />
  ),
  UserPlus: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaUserPlus className={className} size={size} />
  ),
  Phone: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaPhone className={className} size={size} />
  ),

  // Healthcare Icons
  Hospital: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHospital className={className} size={size} />
  ),
  Heart: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHeart className={className} size={size} />
  ),
  HealthAndSafety: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaHeart className={className} size={size} />
  ),
  Stethoscope: ({ className = "w-6 h-6", size }: IconProps) => (
    <FaStethoscope className={className} size={size} />
  ),

  // Navigation & Layout Icons
  Home: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiHome className={className} size={size} />
  ),
  ChartBar: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiChartBar className={className} size={size} />
  ),
  Calendar: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiCalendar className={className} size={size} />
  ),

  // Status & Notification Icons
  Info: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiInformationCircle className={className} size={size} />
  ),
  Success: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiCheckCircle className={className} size={size} />
  ),
  Warning: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiExclamationCircle className={className} size={size} />
  ),
  Error: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiXCircle className={className} size={size} />
  ),

  // Special Effects Icons
  Sparkles: ({ className = "w-6 h-6", size }: IconProps) => (
    <HiSparkles className={className} size={size} />
  ),
};

// Export individual icons for easier usage
export const { 
  WavingHand, 
  Lightbulb, 
  Rocket, 
  Mobile, 
  QRCode, 
  FileFolder, 
  Wrench, 
  Tools, 
  Settings, 
  Palette, 
  Lock, 
  Shield, 
  Users, 
  UserPlus, 
  Phone, 
  Hospital, 
  Heart, 
  HealthAndSafety, 
  Stethoscope, 
  Home, 
  ChartBar, 
  Calendar, 
  Info, 
  Success, 
  Warning, 
  Error, 
  Sparkles 
} = Icons;

export default Icons;
